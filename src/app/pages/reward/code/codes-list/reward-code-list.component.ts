import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { RewardCode, RewardCodeFilter, RewardCodeGroup, RewardCodeService } from '../../../../@core/platform/reward';
import { RewardCodeGroupService } from '../../../../@core/platform/reward';
import { PagedResultDataBaseComponent, UrlParams } from '../../../../shared/components';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PagedResult } from '../../../../@core/data/paged-result';
import { PagedResultDataSourceSettings } from '../../../../@core/data/paged-result-data-source';
import { DateService, FileDownloadService } from '../../../../@core/services';
import { NbDialogService } from '@nebular/theme';
import { RewardCodeListActionComponent } from './reward-code-list-actions.component';

@Component({
    selector: 'ngx-reward-list',
    styleUrls: ['./reward-code-list.component.scss'],
    templateUrl: './reward-code-list.component.html',
})
export class RewardCodeListComponent extends PagedResultDataBaseComponent<RewardCode> implements OnInit, OnDestroy {
    private userSub: Subject<string> = new Subject();
    destroyed = new Subject();

    public code: string;
    public enabled: boolean;
    public redeemed: boolean;

    @Input()
    public groupId: number;

    public settings = {
        actions: null,
        hideSubHeader: true,
        pager: {
            perPage: 10
        },
        columns: {
            id: {
                title: 'ID',
                type: 'number',
            },
            groupId: {
                title: 'Group ID',
                type: 'number',
            },
            code: {
                title: 'Code',
                type: 'string',
            },
            groupCode: {
                title: 'Group Code',
                type: 'string',
            },
            redeemed: {
                title: 'Redeemed',
                type: 'boolean'
            },
            userId: {
                title: 'User ID',
                type: 'number',
            },
            enabled: {
                title: 'Enabled',
                type: 'boolean',
            },
            redeemedTime: {
                title: 'Redeemed Time',
                type: 'date',
                sort: false,
                valuePrepareFunction: (date: Date) => this.dateService.transform(date)
            },
            actions: {
                title: '',
                type: 'custom',
                filter: false,
                sort: false,
                renderComponent: RewardCodeListActionComponent,
                onComponentInitFunction: (instance: RewardCodeListActionComponent) => this.onActionColumnInit(instance)
            }
        }
    };

    constructor(
        protected readonly route: ActivatedRoute,
        protected readonly router: Router,
        private readonly dateService: DateService,
        private readonly rewardCodeService: RewardCodeService,
        private readonly dialog: NbDialogService,
        private readonly fileDownloadService: FileDownloadService) {
        super(route, router);
    }

    public ngOnInit(): void {
        super.ngOnInit();
    }

    public ngOnDestroy(): void {
        this.destroyed.next();
        this.destroyed.complete();
        this.userSub.unsubscribe();
        this.userSub = undefined;
    }

    protected load(settings: PagedResultDataSourceSettings): Observable<PagedResult<RewardCode>> {
        const filter: RewardCodeFilter = {
            groupId: this.groupId,
            code: this.code,
            enabled: this.enabled,
            redeemed: this.redeemed,
            page: settings.page,
            pageSize: settings.pageSize,
            order: settings.order,
            direction: settings.direction,
        };

        console.log('Loading data...', settings);
        return this.rewardCodeService.getAllByGroup(filter);
    }

    protected getUrlParams(page: number, order?: string, direction?: 'ASC' | 'DESC'): Params {
        return {
            groupId: this.groupId || null,
            page: page || null,
            pageSize: this.pageSize || null,
            code: this.code || null,
            enabled: this.enabled || null,
            redeemed: this.redeemed || null,
            order: order || null,
            orderDir: direction || null
        };
    }

    protected onUrlParamChange(params: UrlParams): void {
        this.datasource.setPage(this.page ?? 1, false);
        this.datasource.enabled = true;
    }

    public onFilterChange(): void {
        this.datasource.setPage(1);
    }

    public onResetFilter(): void {
        this.code = undefined;
        this.enabled = undefined;
        this.redeemed = undefined;
        this.onFilterChange();
    }

    public async onExport(): Promise<void> {
        if (this.totalCount === 0) {
            return;
        }

        this.loading = true;

        try {
            // Get all data for export
            const filter: RewardCodeFilter = {
                groupId: this.groupId,
                code: this.code,
                enabled: this.enabled,
                redeemed: this.redeemed,
                page: 1,
                pageSize: this.totalCount,
                order: this.datasource.getSort().field,
                direction: this.datasource.getSort().direction === 'asc' ? 'ASC' : 'DESC',
            };

            const result = await this.rewardCodeService.getAllByGroup(filter).toPromise();

            // Prepare data for CSV export
            const exportData = result.items.map((rewardCode: RewardCode) => ({
                ID: rewardCode.id,
                'Group ID': rewardCode.groupId,
                Code: rewardCode.code,
                'Group Code': rewardCode.groupCode,
                Redeemed: rewardCode.redeemed,
                'User ID': rewardCode.userId || '',
                Enabled: rewardCode.enabled,
                'Redeemed Time': rewardCode.redeemedTime ? this.dateService.transform(rewardCode.redeemedTime) : '',
                'Created Time': this.dateService.transform(rewardCode.createTime),
                'Updated Time': this.dateService.transform(rewardCode.updateTime)
            }));

            // Generate filename with current date and groupCode
            const currentDate = this.dateService.formatNow('YYYY-MM-DD');
            const groupCodePart = result.items.length > 0 && result.items[0].groupCode
                ? `${result.items[0].groupCode}_`
                : this.groupId ? `Group_${this.groupId}_` : '';
            const filename = `Reward_Codes_${groupCodePart}${currentDate}`;

            // Export to CSV
            this.fileDownloadService.export(exportData, filename);
        } catch (error) {
            console.error('Error exporting reward codes:', error);
        } finally {
            this.loading = false;
        }
    }

    private onActionColumnInit(instance: RewardCodeListActionComponent): void {
        instance.codeId = instance.rowData?.id;
        instance.enabled = instance.rowData?.enabled;

        instance.loadingChange
            .pipe(
                takeUntil(this.destroyed)
            )
            .subscribe((value: boolean) => {
                this.loading = value;
            });

        instance.updated
            .pipe(
                takeUntil(this.destroyed)
            )
            .subscribe((value: boolean) => {
                this.datasource.refresh();
                return;
            });
    }
}