@import "../../../../@theme/styles/themes";

:host {
  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-buttons {
      display: flex;
      gap: 10px;
      
      button {
        display: flex;
        align-items: center;
        
        nb-icon {
          margin-right: 6px;
        }
      }
    }
  }

  .card-footer {
    display: flex;
    padding: 1rem;
    
    .add-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      nb-icon {
        font-size: 1rem;
      }
    }
  }

  #filter {
    hr {
      border-top: 1px solid #151a30;
      margin-top: 1rem;
      margin-bottom: 1.5rem;
    }

    .suffix-button {
      border-radius: 0 0.25rem 0.25rem 0;
      padding: 11px 13px;
    }

    ::ng-deep nb-tooltip .content {
      font-weight: 800;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
      justify-content: flex-end;
      align-items: stretch;
      width: 100%;
      padding: 27px 0;

      @media (min-width: 1200px) {
        flex-direction: row;
        align-items: center;
        gap: 6px;
      }

      button {
        flex: 1;
        min-width: 0;
        white-space: nowrap;

        @media (min-width: 1200px) {
          flex: none;
          min-width: auto;
        }

        .button-text {
          @media (max-width: 1399px) {
            display: none;
          }
        }

        nb-icon {
          margin-right: 0;

          @media (min-width: 1400px) {
            margin-right: 6px;
          }
        }
      }
    }
  }

  .time {
    position: absolute;
    font-weight: 600;
    font-size: 14px;
    color: #8f9bb3;
  }

  ::ng-deep ngx-paged-result-data-header {
    font-size: 14px;
    font-weight: 600;
    color: nb-theme(color-basic-600);

    span {
      font-weight: 800;
      color: #fff;
    }
  }

  ::ng-deep ng2-smart-table {
    input[type="checkbox"] {
      display: inline;
      width: auto;
      cursor: pointer;
      zoom: 1.12;
      position: relative;
      top: 3px;

      &:focus {
        outline: hidden;
        box-shadow: none;
      }
    }

    nav.ng2-smart-pagination-nav {
      margin-top: 1.5rem;

      ul {
        margin-bottom: 0;

        li>span,
        li>a {
          font-size: 14px !important;
          padding: 0.6rem 1rem !important;
        }
      }
    }

    &.no-data {
      thead th:last-child {
        display: none;
      }

      tbody td {
        padding: 0.8em;

        &:first-child {
          text-align: center;
          font-size: 14px;
          font-weight: 800;
          padding: 1.75em;
          color: nb-theme(color-basic-200);
        }
      }
    }

    thead tr th {
      cursor: default;
      padding: 0.6em !important;
      position: relative;

      a,
      span {
        font-weight: 800;
        font-size: 14px;

        &.sort,
        &:hover,
        &:active,
        &:focus {
          font-weight: 800 !important;
          color: #fff;
        }

        &:after {
          position: absolute !important;
          top: 14px;
          right: 8px;
        }

        &.desc:after {
          position: absolute !important;
          top: 14px;
          right: 8px;
        }
      }
    }

    tbody {
      tr td {
        font-size: 14px;
        padding: 0.6em;
        font-weight: 600;
        cursor: default;
        text-transform: capitalize;

        .img-container {
          height: 30px;
          width: 75px;
          display: flex;
          align-items: center;
          align-content: center;
          overflow: hidden;
          background-color: #1d2442;
          font-size: 11px;

          &:before {
            content: "\f110";
            font-family: "Font Awesome 5 Free";
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -10px;
            margin-left: -5.5px;
            color: #8f9bb3;
            z-index: 1;
            animation: spin 1s infinite linear;
            opacity: .7;
          }

          img {
            position: relative;
            z-index: 2;
          }
        }

        .name-container {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 800;

          @media (min-width: 1800px) {
            max-width: 100%;
          }
        }

        .prize-container {
          max-width: 75px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 800;

          @media (min-width: 1800px) {
            max-width: 100%;
          }
        }

        .badge {
          font-weight: 800;
          font-size: 12px;
          white-space: nowrap;
          padding: 4px 8px;
          margin: 1px 1px 1px 0;
        }

        &:nth-child(3) {
          width: 90px;
        }

        &:nth-child(7) {
          white-space: nowrap;
        }

        &:last-child {
          width: 47px;
          text-align: center;
        }
      }
    }
  }

  ::ng-deep nb-spinner {
    z-index: 1000;
  }

  .table-responsive {
    overflow-x: auto;
    width: 100%;
    
    ::ng-deep {
      table {
        width: auto;
        min-width: 100%;
        
        th, td {
          white-space: nowrap;
          padding: 0.6em 1em !important;
        }
        
        th {
          text-align: left;
        }
      }
    }
  }
}
