<div id="reward-list" class="row">
  <div class="col-md-12">
    <nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="giant" nbSpinnerMessage="">
      <nb-card-header class="card-header">
        <span>
          Reward Codes
        </span>
      </nb-card-header>
      <nb-card-body class="card-body">
        <div id="filter">
          <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-12">
              <div class="form-group">
                <label class="label" for="code">
                  Code
                </label>
                <nb-form-field>
                  <input name="code" type="text" [(ngModel)]="code" [(ngModel)]="code" placeholder="Enter Code" nbInput
                    fullWidth />
                </nb-form-field>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12">
              <div class="form-group">
                <label class="label" for="statuses">
                  Status
                </label>
                <nb-select name="statuses" fullWidth placeholder="Any" [(ngModel)]="enabled">
                  <nb-option value=true>Enabled</nb-option>
                  <nb-option value=false>Disabled</nb-option>
                </nb-select>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12">
              <div class="form-group">
                <label class="label" for="redeemed">
                  Redeemed
                </label>
                <nb-select name="redeemed" fullWidth placeholder="Any" [(ngModel)]="redeemed">
                  <nb-option value=true>True</nb-option>
                  <nb-option value=false>False</nb-option>
                </nb-select>
              </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12">
              <div class="actions">
                <button class="reset-button" nbButton size="small" [disabled]="loading" (click)="onResetFilter()">
                  <nb-icon icon="close"></nb-icon>
                  <span class="button-text">Reset</span>
                </button>
                <button class="filter-button" nbButton size="small" status="success" [disabled]="loading" (click)="onFilterChange()">
                  <nb-icon icon="search"></nb-icon>
                  <span class="button-text">Filter</span>
                </button>
                <button class="export-button" nbButton size="small" status="info" [disabled]="loading || totalCount <= 0" (click)="onExport()" title="Download CSV">
                  <nb-icon icon="download-outline"></nb-icon>
                  <span class="button-text">Export</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </nb-card-body>
      <nb-card-body class="card-body">
        <ngx-paged-result-data-header [page]="page" [pageCount]="pageCount" [totalCount]="totalCount"
          [loading]="loading"></ngx-paged-result-data-header>
        <div class="table-responsive">
          <ng2-smart-table [settings]="settings" [source]="datasource"
            [class.no-data]="totalCount <= 0"></ng2-smart-table>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
</div>
<ng-template #actionsTemplate let-row="row">
  <button nbButton ghost size="small" (click)="onAddCodesToGroup(row)" title="Add Codes">
    <nb-icon icon="plus-outline"></nb-icon>
  </button>
</ng-template>